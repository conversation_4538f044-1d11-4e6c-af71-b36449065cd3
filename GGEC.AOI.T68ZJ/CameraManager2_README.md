# CameraManager2 使用说明

## 概述

CameraManager2 是参考 BasicDemoLineScan 官方示例重新实现的相机管理器，旨在提供更直接、更可靠的视频预览和拍照功能。它结合了 BasicDemoLineScan 的简洁性和现有 CameraManager 的多设备支持能力。

## 主要特性

### 1. 设备管理
- 支持多设备同时管理（默认支持2个设备）
- 自动设备枚举和初始化
- GigE设备网络参数优化
- 完善的错误处理和设备重连机制

### 2. 视频预览
- 参考 BasicDemoLineScan 的 `ReceiveThreadProcess` 实现
- 使用 `ImageRender.DisplayOneFrame` 直接渲染到 PictureBox
- 低延迟实时预览
- 独立的接收线程管理

### 3. 拍照功能
- 实时图像缓存机制
- 支持单设备和多设备同步拍照
- 多种图像格式保存（BMP、JPEG、PNG）
- 自动文件路径生成

### 4. 兼容性
- 与现有 CameraManager 接口兼容
- 支持传感器触发拍照
- 保持相同的公共属性和方法

## 使用方法

### 基本使用流程

```csharp
// 1. 获取实例
var cameraManager = CameraManager2.Instance;

// 2. 初始化相机系统
cameraManager.InitCameras();

// 3. 打开设备（默认打开2个设备）
cameraManager.OpenDevices(2);

// 4. 开始预览
cameraManager.StartLivePreview(pictureBox1, pictureBox2);

// 5. 拍照
var (image1, image2) = cameraManager.TakeSensorTriggeredPhotos();

// 6. 保存图像
cameraManager.SaveJpegImage(0, 90); // 保存相机0的图像，质量90%
cameraManager.SaveJpegImage(1, 90); // 保存相机1的图像，质量90%

// 7. 停止预览
cameraManager.StopLivePreview();

// 8. 关闭设备
cameraManager.CloseDevices();
```

### 参数配置

```csharp
// 设置曝光时间（微秒）
cameraManager.ExposureTime = 2000.0;

// 设置增益
cameraManager.Gain = 20.0;

// 设置像素格式
cameraManager.PixelFormat = "Mono8";

// 启用自动曝光
cameraManager.AutoExposureEnabled = true;

// 启用自动增益
cameraManager.AutoGainEnabled = true;
```

## 与原 CameraManager 的对比

### 优势

1. **更直接的预览实现**
   - 使用 `ImageRender.DisplayOneFrame` 直接渲染
   - 减少了复杂的缓存和同步机制
   - 降低了预览延迟

2. **简化的线程管理**
   - 每个设备独立的接收线程
   - 清晰的线程生命周期管理
   - 更好的错误隔离

3. **更可靠的图像获取**
   - 参考官方示例的实现方式
   - 减少了内存泄漏的风险
   - 更好的资源管理

### 潜在问题和解决方案

#### 1. 线程安全问题

**问题描述：** 多线程访问共享资源可能导致竞态条件

**解决方案：**
- 使用 `lock` 语句保护关键代码段
- 分离读写操作的锁对象
- 使用线程安全的集合类型

```csharp
// 示例：安全的图像访问
lock (lockForSaveImage)
{
    if (deviceIndex < framesForSave.Count && framesForSave[deviceIndex]?.Image != null)
    {
        var image = framesForSave[deviceIndex].Image;
        // 处理图像...
    }
}
```

#### 2. 内存管理问题

**问题描述：** 图像数据占用大量内存，可能导致内存泄漏

**解决方案：**
- 及时释放 `IFrameOut` 对象
- 使用 `using` 语句管理资源
- 定期清理缓存的图像数据

```csharp
// 示例：正确的资源管理
IFrameOut frameOut = null;
try
{
    int result = device.StreamGrabber.GetImageBuffer(1000, out frameOut);
    if (result == MvError.MV_OK)
    {
        // 处理图像...
    }
}
finally
{
    frameOut?.Dispose(); // 确保资源被释放
}
```

#### 3. 设备连接稳定性

**问题描述：** 网络设备可能出现连接中断

**解决方案：**
- 实现设备状态监控
- 自动重连机制
- 错误恢复策略

```csharp
// 示例：错误处理
try
{
    int result = device.StreamGrabber.GetImageBuffer(1000, out frameOut);
    if (result != MvError.MV_OK)
    {
        Logger.Warn($"获取图像失败：{GetErrorMessage(result)}");
        // 可以在这里实现重连逻辑
    }
}
catch (Exception ex)
{
    Logger.Exception(ex, "设备通信异常");
    // 实现设备重连
}
```

#### 4. UI 线程安全

**问题描述：** 在工作线程中更新 UI 控件可能导致异常

**解决方案：**
- 使用 `Invoke` 方法在 UI 线程中更新控件
- 检查控件的 `IsDisposed` 状态

```csharp
// 示例：安全的 UI 更新
if (pictureBox != null && !pictureBox.IsDisposed)
{
    if (pictureBox.InvokeRequired)
    {
        pictureBox.Invoke(new Action(() => 
        {
            device.ImageRender.DisplayOneFrame(pictureBox.Handle, frameOut.Image);
        }));
    }
    else
    {
        device.ImageRender.DisplayOneFrame(pictureBox.Handle, frameOut.Image);
    }
}
```

## 测试建议

### 1. 功能测试
- 使用 `TestCameraManager2Form` 进行基本功能测试
- 验证设备初始化、预览、拍照、保存等功能
- 测试多设备同步工作

### 2. 性能测试
- 监控内存使用情况
- 测试长时间运行的稳定性
- 验证预览帧率和延迟

### 3. 压力测试
- 频繁启停预览
- 大量拍照操作
- 网络中断恢复测试

## 部署注意事项

1. **依赖项检查**
   - 确保 MvCameraControl SDK 正确安装
   - 验证相机驱动程序版本

2. **权限设置**
   - 确保应用程序有足够的文件写入权限
   - 网络设备需要正确的网络配置

3. **性能优化**
   - 根据实际需求调整图像格式和分辨率
   - 优化网络参数（GigE设备）
   - 合理设置线程优先级

## 总结

CameraManager2 通过参考官方示例的实现方式，提供了更可靠和直接的相机管理功能。它在保持与现有代码兼容性的同时，解决了原有实现中的一些复杂性问题。通过合理的测试和部署，可以有效提升系统的稳定性和性能。
