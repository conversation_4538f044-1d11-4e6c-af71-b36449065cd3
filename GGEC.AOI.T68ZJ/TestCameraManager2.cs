using System;
using System.Windows.Forms;
using GGEC.AOI.T68ZJ.Managers;
using GGEC.AOI.T68ZJ.Log;
using MvCameraControl;

namespace GGEC.AOI.T68ZJ
{
    /// <summary>
    /// CameraManager2 测试程序
    /// 用于验证新的相机管理器功能
    /// </summary>
    public partial class TestCameraManager2Form : Form
    {
        private CameraManager2 cameraManager;
        private PictureBox pictureBox1;
        private PictureBox pictureBox2;
        private Button btnInitialize;
        private Button btnOpenDevices;
        private Button btnStartPreview;
        private Button btnStopPreview;
        private Button btnTakePhoto;
        private Button btnSaveImage;
        private Button btnCloseDevices;
        private Label lblStatus;
        private TextBox txtLog;

        public TestCameraManager2Form()
        {
            InitializeComponent();
            cameraManager = CameraManager2.Instance;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form 设置
            this.Text = "CameraManager2 测试程序";
            this.Size = new System.Drawing.Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;

            // PictureBox1
            this.pictureBox1 = new PictureBox();
            this.pictureBox1.Location = new System.Drawing.Point(12, 12);
            this.pictureBox1.Size = new System.Drawing.Size(400, 300);
            this.pictureBox1.BorderStyle = BorderStyle.FixedSingle;
            this.pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            this.pictureBox1.BackColor = System.Drawing.Color.Black;
            this.Controls.Add(this.pictureBox1);

            // PictureBox2
            this.pictureBox2 = new PictureBox();
            this.pictureBox2.Location = new System.Drawing.Point(430, 12);
            this.pictureBox2.Size = new System.Drawing.Size(400, 300);
            this.pictureBox2.BorderStyle = BorderStyle.FixedSingle;
            this.pictureBox2.SizeMode = PictureBoxSizeMode.Zoom;
            this.pictureBox2.BackColor = System.Drawing.Color.Black;
            this.Controls.Add(this.pictureBox2);

            // 按钮区域
            int buttonY = 330;
            int buttonSpacing = 100;

            // 初始化按钮
            this.btnInitialize = new Button();
            this.btnInitialize.Location = new System.Drawing.Point(12, buttonY);
            this.btnInitialize.Size = new System.Drawing.Size(80, 30);
            this.btnInitialize.Text = "初始化";
            this.btnInitialize.Click += BtnInitialize_Click;
            this.Controls.Add(this.btnInitialize);

            // 打开设备按钮
            this.btnOpenDevices = new Button();
            this.btnOpenDevices.Location = new System.Drawing.Point(12 + buttonSpacing, buttonY);
            this.btnOpenDevices.Size = new System.Drawing.Size(80, 30);
            this.btnOpenDevices.Text = "打开设备";
            this.btnOpenDevices.Enabled = false;
            this.btnOpenDevices.Click += BtnOpenDevices_Click;
            this.Controls.Add(this.btnOpenDevices);

            // 开始预览按钮
            this.btnStartPreview = new Button();
            this.btnStartPreview.Location = new System.Drawing.Point(12 + buttonSpacing * 2, buttonY);
            this.btnStartPreview.Size = new System.Drawing.Size(80, 30);
            this.btnStartPreview.Text = "开始预览";
            this.btnStartPreview.Enabled = false;
            this.btnStartPreview.Click += BtnStartPreview_Click;
            this.Controls.Add(this.btnStartPreview);

            // 停止预览按钮
            this.btnStopPreview = new Button();
            this.btnStopPreview.Location = new System.Drawing.Point(12 + buttonSpacing * 3, buttonY);
            this.btnStopPreview.Size = new System.Drawing.Size(80, 30);
            this.btnStopPreview.Text = "停止预览";
            this.btnStopPreview.Enabled = false;
            this.btnStopPreview.Click += BtnStopPreview_Click;
            this.Controls.Add(this.btnStopPreview);

            // 拍照按钮
            this.btnTakePhoto = new Button();
            this.btnTakePhoto.Location = new System.Drawing.Point(12 + buttonSpacing * 4, buttonY);
            this.btnTakePhoto.Size = new System.Drawing.Size(80, 30);
            this.btnTakePhoto.Text = "拍照";
            this.btnTakePhoto.Enabled = false;
            this.btnTakePhoto.Click += BtnTakePhoto_Click;
            this.Controls.Add(this.btnTakePhoto);

            // 保存图像按钮
            this.btnSaveImage = new Button();
            this.btnSaveImage.Location = new System.Drawing.Point(12 + buttonSpacing * 5, buttonY);
            this.btnSaveImage.Size = new System.Drawing.Size(80, 30);
            this.btnSaveImage.Text = "保存图像";
            this.btnSaveImage.Enabled = false;
            this.btnSaveImage.Click += BtnSaveImage_Click;
            this.Controls.Add(this.btnSaveImage);

            // 关闭设备按钮
            this.btnCloseDevices = new Button();
            this.btnCloseDevices.Location = new System.Drawing.Point(12 + buttonSpacing * 6, buttonY);
            this.btnCloseDevices.Size = new System.Drawing.Size(80, 30);
            this.btnCloseDevices.Text = "关闭设备";
            this.btnCloseDevices.Enabled = false;
            this.btnCloseDevices.Click += BtnCloseDevices_Click;
            this.Controls.Add(this.btnCloseDevices);

            // 状态标签
            this.lblStatus = new Label();
            this.lblStatus.Location = new System.Drawing.Point(12, buttonY + 40);
            this.lblStatus.Size = new System.Drawing.Size(800, 20);
            this.lblStatus.Text = "状态：未初始化";
            this.Controls.Add(this.lblStatus);

            // 日志文本框
            this.txtLog = new TextBox();
            this.txtLog.Location = new System.Drawing.Point(12, buttonY + 70);
            this.txtLog.Size = new System.Drawing.Size(1160, 300);
            this.txtLog.Multiline = true;
            this.txtLog.ScrollBars = ScrollBars.Vertical;
            this.txtLog.ReadOnly = true;
            this.txtLog.Font = new System.Drawing.Font("Consolas", 9);
            this.Controls.Add(this.txtLog);

            this.ResumeLayout(false);
        }

        private void BtnInitialize_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("正在初始化相机系统...");
                LogMessage("开始初始化 CameraManager2");

                cameraManager.InitCameras();

                UpdateStatus("相机系统初始化完成");
                LogMessage("CameraManager2 初始化成功");

                btnInitialize.Enabled = false;
                btnOpenDevices.Enabled = true;
            }
            catch (Exception ex)
            {
                UpdateStatus($"初始化失败：{ex.Message}");
                LogMessage($"初始化失败：{ex.Message}");
                MessageBox.Show($"初始化失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnOpenDevices_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("正在打开设备...");
                LogMessage("开始打开设备");

                cameraManager.OpenDevices(2); // 打开2个设备

                UpdateStatus($"设备打开完成，共 {cameraManager.DeviceCount} 个设备");
                LogMessage($"设备打开成功，设备数量：{cameraManager.DeviceCount}");

                btnOpenDevices.Enabled = false;
                btnStartPreview.Enabled = true;
                btnTakePhoto.Enabled = true;
                btnCloseDevices.Enabled = true;
            }
            catch (Exception ex)
            {
                UpdateStatus($"打开设备失败：{ex.Message}");
                LogMessage($"打开设备失败：{ex.Message}");
                MessageBox.Show($"打开设备失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnStartPreview_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("正在启动预览...");
                LogMessage("开始启动预览");

                cameraManager.StartLivePreview(pictureBox1, pictureBox2);

                UpdateStatus("预览已启动");
                LogMessage("预览启动成功");

                btnStartPreview.Enabled = false;
                btnStopPreview.Enabled = true;
                btnSaveImage.Enabled = true;
            }
            catch (Exception ex)
            {
                UpdateStatus($"启动预览失败：{ex.Message}");
                LogMessage($"启动预览失败：{ex.Message}");
                MessageBox.Show($"启动预览失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnStopPreview_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("正在停止预览...");
                LogMessage("开始停止预览");

                cameraManager.StopLivePreview();

                UpdateStatus("预览已停止");
                LogMessage("预览停止成功");

                btnStartPreview.Enabled = true;
                btnStopPreview.Enabled = false;
                btnSaveImage.Enabled = false;
            }
            catch (Exception ex)
            {
                UpdateStatus($"停止预览失败：{ex.Message}");
                LogMessage($"停止预览失败：{ex.Message}");
                MessageBox.Show($"停止预览失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnTakePhoto_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("正在拍照...");
                LogMessage("开始拍照");

                var (image1, image2) = cameraManager.TakeSensorTriggeredPhotos();

                string result = $"拍照完成 - 相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}";
                UpdateStatus(result);
                LogMessage(result);

                if (image1 != null)
                {
                    LogMessage($"相机1图像尺寸：{image1.Width}x{image1.Height}");
                }
                if (image2 != null)
                {
                    LogMessage($"相机2图像尺寸：{image2.Width}x{image2.Height}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"拍照失败：{ex.Message}");
                LogMessage($"拍照失败：{ex.Message}");
                MessageBox.Show($"拍照失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveImage_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("正在保存图像...");
                LogMessage("开始保存图像");

                int result1 = cameraManager.SaveJpegImage(0, 90);
                int result2 = cameraManager.DeviceCount > 1 ? cameraManager.SaveJpegImage(1, 90) : MvError.MV_OK;

                string resultMsg = $"保存图像完成 - 相机1：{(result1 == MvError.MV_OK ? "成功" : "失败")}";
                if (cameraManager.DeviceCount > 1)
                {
                    resultMsg += $"，相机2：{(result2 == MvError.MV_OK ? "成功" : "失败")}";
                }

                UpdateStatus(resultMsg);
                LogMessage(resultMsg);
            }
            catch (Exception ex)
            {
                UpdateStatus($"保存图像失败：{ex.Message}");
                LogMessage($"保存图像失败：{ex.Message}");
                MessageBox.Show($"保存图像失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCloseDevices_Click(object sender, EventArgs e)
        {
            try
            {
                UpdateStatus("正在关闭设备...");
                LogMessage("开始关闭设备");

                cameraManager.CloseDevices();

                UpdateStatus("设备已关闭");
                LogMessage("设备关闭成功");

                btnOpenDevices.Enabled = true;
                btnStartPreview.Enabled = false;
                btnStopPreview.Enabled = false;
                btnTakePhoto.Enabled = false;
                btnSaveImage.Enabled = false;
                btnCloseDevices.Enabled = false;
            }
            catch (Exception ex)
            {
                UpdateStatus($"关闭设备失败：{ex.Message}");
                LogMessage($"关闭设备失败：{ex.Message}");
                MessageBox.Show($"关闭设备失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatus(string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(UpdateStatus), status);
                return;
            }

            lblStatus.Text = $"状态：{status}";
        }

        private void LogMessage(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(LogMessage), message);
                return;
            }

            string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            txtLog.AppendText($"[{timestamp}] {message}\r\n");
            txtLog.ScrollToCaret();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                cameraManager?.Dispose();
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "关闭测试窗口时发生异常");
            }
            base.OnFormClosing(e);
        }
    }
}
