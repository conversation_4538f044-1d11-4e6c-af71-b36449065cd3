using System;
using System.Windows.Forms;
using GGEC.AOI.T68ZJ.Managers;
using GGEC.AOI.T68ZJ.Log;
using MvCameraControl;

namespace GGEC.AOI.T68ZJ
{
    /// <summary>
    /// CameraManager 使用示例
    /// 展示如何在 CameraManager 和 CameraManager2 之间切换
    /// </summary>
    public class CameraManagerUsageExample
    {
        private ICameraManager cameraManager;
        private PictureBox pictureBox1;
        private PictureBox pictureBox2;

        public CameraManagerUsageExample()
        {
            // 从配置文件加载相机管理器类型
            CameraManagerFactory.LoadFromConfig();
            
            // 获取相机管理器实例
            cameraManager = CameraManagerFactory.GetInstance();
            
            Logger.Info($"使用相机管理器类型：{CameraManagerFactory.CurrentType}");
        }

        /// <summary>
        /// 示例1：基本使用流程
        /// </summary>
        public void BasicUsageExample()
        {
            try
            {
                Logger.Info("开始基本使用示例");

                // 1. 初始化相机系统
                cameraManager.InitCameras();

                // 2. 配置参数
                cameraManager.ExposureTime = 2000.0;
                cameraManager.Gain = 20.0;
                cameraManager.PixelFormat = "Mono8";
                cameraManager.AutoExposureEnabled = false;

                // 3. 打开设备
                cameraManager.OpenDevices(2);

                // 4. 开始预览（假设已有 PictureBox 控件）
                if (pictureBox1 != null && pictureBox2 != null)
                {
                    cameraManager.StartLivePreview(pictureBox1, pictureBox2);
                }

                // 5. 拍照
                var (image1, image2) = cameraManager.TakeSensorTriggeredPhotos();
                if (image1 != null && image2 != null)
                {
                    Logger.Info($"拍照成功 - 相机1: {image1.Width}x{image1.Height}, 相机2: {image2.Width}x{image2.Height}");
                }

                // 6. 保存图像
                cameraManager.SaveJpegImage(0, 90);
                cameraManager.SaveJpegImage(1, 90);

                Logger.Info("基本使用示例完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "基本使用示例失败");
            }
        }

        /// <summary>
        /// 示例2：运行时切换相机管理器类型
        /// </summary>
        public void SwitchManagerTypeExample()
        {
            try
            {
                Logger.Info("开始切换管理器类型示例");

                // 停止当前预览
                cameraManager.StopLivePreview();
                cameraManager.CloseDevices();

                // 切换到另一种类型
                var newType = CameraManagerFactory.CurrentType == CameraManagerFactory.CameraManagerType.Original
                    ? CameraManagerFactory.CameraManagerType.Enhanced
                    : CameraManagerFactory.CameraManagerType.Original;

                Logger.Info($"切换相机管理器类型：{CameraManagerFactory.CurrentType} -> {newType}");
                CameraManagerFactory.CurrentType = newType;

                // 获取新的管理器实例
                cameraManager = CameraManagerFactory.GetInstance();

                // 重新初始化
                cameraManager.InitCameras();
                cameraManager.OpenDevices(2);

                if (pictureBox1 != null && pictureBox2 != null)
                {
                    cameraManager.StartLivePreview(pictureBox1, pictureBox2);
                }

                Logger.Info("切换管理器类型示例完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "切换管理器类型示例失败");
            }
        }

        /// <summary>
        /// 示例3：性能对比测试
        /// </summary>
        public void PerformanceComparisonExample()
        {
            try
            {
                Logger.Info("开始性能对比测试");

                var types = new[] 
                { 
                    CameraManagerFactory.CameraManagerType.Original,
                    CameraManagerFactory.CameraManagerType.Enhanced
                };

                foreach (var type in types)
                {
                    Logger.Info($"测试相机管理器类型：{type}");

                    // 切换类型
                    CameraManagerFactory.CurrentType = type;
                    var manager = CameraManagerFactory.GetInstance();

                    // 初始化
                    var initStart = DateTime.Now;
                    manager.InitCameras();
                    manager.OpenDevices(2);
                    var initTime = DateTime.Now - initStart;

                    Logger.Info($"{type} 初始化耗时：{initTime.TotalMilliseconds:F0}ms");

                    // 预览测试
                    if (pictureBox1 != null && pictureBox2 != null)
                    {
                        var previewStart = DateTime.Now;
                        manager.StartLivePreview(pictureBox1, pictureBox2);
                        
                        // 等待预览稳定
                        System.Threading.Thread.Sleep(1000);
                        
                        var previewTime = DateTime.Now - previewStart;
                        Logger.Info($"{type} 预览启动耗时：{previewTime.TotalMilliseconds:F0}ms");

                        // 拍照性能测试
                        var photoTimes = new List<double>();
                        for (int i = 0; i < 10; i++)
                        {
                            var photoStart = DateTime.Now;
                            var (image1, image2) = manager.TakeSensorTriggeredPhotos();
                            var photoTime = DateTime.Now - photoStart;
                            
                            if (image1 != null && image2 != null)
                            {
                                photoTimes.Add(photoTime.TotalMilliseconds);
                            }
                            
                            System.Threading.Thread.Sleep(100); // 避免过于频繁
                        }

                        if (photoTimes.Count > 0)
                        {
                            var avgPhotoTime = photoTimes.Average();
                            Logger.Info($"{type} 平均拍照耗时：{avgPhotoTime:F1}ms");
                        }

                        manager.StopLivePreview();
                    }

                    // 内存使用情况
                    var memoryUsage = manager.GetMemoryUsage();
                    Logger.Info($"{type} 内存使用：{memoryUsage}");

                    manager.CloseDevices();
                    
                    // 等待资源释放
                    System.Threading.Thread.Sleep(500);
                }

                Logger.Info("性能对比测试完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "性能对比测试失败");
            }
        }

        /// <summary>
        /// 示例4：错误处理和恢复
        /// </summary>
        public void ErrorHandlingExample()
        {
            try
            {
                Logger.Info("开始错误处理示例");

                // 模拟各种错误情况
                
                // 1. 无设备情况
                try
                {
                    cameraManager.TakeSensorTriggeredPhotos();
                }
                catch (Exception ex)
                {
                    Logger.Warn($"无设备拍照测试：{ex.Message}");
                }

                // 2. 重复初始化
                try
                {
                    cameraManager.InitCameras();
                    cameraManager.InitCameras(); // 重复调用
                }
                catch (Exception ex)
                {
                    Logger.Warn($"重复初始化测试：{ex.Message}");
                }

                // 3. 资源清理测试
                cameraManager.CloseDevices();
                cameraManager.CloseDevices(); // 重复关闭

                Logger.Info("错误处理示例完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "错误处理示例失败");
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            try
            {
                Logger.Info("开始清理资源");

                cameraManager?.StopLivePreview();
                cameraManager?.CloseDevices();
                CameraManagerFactory.Dispose();

                Logger.Info("资源清理完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "清理资源时发生异常");
            }
        }

        /// <summary>
        /// 设置预览控件
        /// </summary>
        public void SetPreviewControls(PictureBox pb1, PictureBox pb2)
        {
            pictureBox1 = pb1;
            pictureBox2 = pb2;
        }
    }

    /// <summary>
    /// 在现有 MainForm 中的使用示例
    /// </summary>
    public partial class MainFormExample : Form
    {
        private ICameraManager cameraManager;

        private void InitializeCameraManager()
        {
            // 从配置加载
            CameraManagerFactory.LoadFromConfig();
            
            // 获取实例
            cameraManager = CameraManagerFactory.GetInstance();
            
            // 初始化
            cameraManager.InitCameras();
            cameraManager.OpenDevices(2);
        }

        private void bt_video_Click(object sender, EventArgs e)
        {
            try
            {
                if (cameraManager.ShowLivePreview)
                {
                    // 停止预览
                    cameraManager.StopLivePreview();
                    // 更新UI...
                }
                else
                {
                    // 开始预览
                    cameraManager.StartLivePreview(pictureBox1, pictureBox2);
                    // 更新UI...
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "预览操作失败");
                MessageBox.Show($"预览操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void bt_photo_Click(object sender, EventArgs e)
        {
            try
            {
                var (image1, image2) = cameraManager.TakeSensorTriggeredPhotos();
                
                if (image1 != null && image2 != null)
                {
                    // 保存图像
                    cameraManager.SaveJpegImage(0, 90);
                    cameraManager.SaveJpegImage(1, 90);
                    
                    Logger.Info("拍照并保存成功");
                }
                else
                {
                    Logger.Warn("拍照失败，未获取到有效图像");
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "拍照操作失败");
                MessageBox.Show($"拍照操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                cameraManager?.StopLivePreview();
                cameraManager?.CloseDevices();
                CameraManagerFactory.Dispose();
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "关闭窗口时清理资源失败");
            }
            
            base.OnFormClosing(e);
        }
    }
}
